import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
import { useDayCycleOptional } from '../Context/DayCycleContext';
// 🎨 CISCO: IMPORT DES VRAIES PALETTES - Remplacement des couleurs pastels
import { VRAIES_PALETTES_CISCO, getVraiePalette, getVraiDegradeCss } from './Palettes-couleurs/vraies-palettes-cisco';

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🔧 SYSTÈME DE PILOTAGE MANUEL SIMPLIFIÉ
// Types pour les modes de fond prédéfinis
type BackgroundMode = 
  | 'dawn'        // Aube
  | 'sunrise'     // Lever du soleil
  | 'morning'     // Matin
  | 'midday'      // Midi
  | 'afternoon'   // Après-midi
  | 'sunset'      // Coucher du soleil
  | 'dusk'        // Crépuscule
  | 'night';      // Nuit

// 🎨 CISCO: VRAIES PALETTES CISCO - Remplacement complet des couleurs pastels
// ✨ NOUVELLES COULEURS: Extraites des vraies captures d'écran de ciels naturels
// Dégradés fluides synchronisés avec le temporisateur de journée

const BACKGROUND_MODES = {
  // 🌌 === MODE NUIT PROFONDE === 🌌
  // CISCO: Vraies couleurs extraites - Bleu nuit naturel
  night: {
    primary: '#020a10',    // Bas de l'écran : Bleu nuit très sombre (horizon)
    secondary: '#03213c',  // Milieu de l'écran : Bleu nuit intermédiaire
    tertiary: '#05315b'    // Haut de l'écran : Bleu nuit plus clair (zénith)
  },

  // 🌅 === MODE AUBE === 🌅
  // CISCO: Dégradé qui MONTE - Soleil arrive à l'horizon, ciel encore sombre en haut
  dawn: {
    primary: '#ffa366',    // Bas de l'écran : Orange rosé clair (horizon - aube arrive)
    secondary: '#6b7280',  // Milieu de l'écran : Gris bleu (transition)
    tertiary: '#1f2937'    // Haut de l'écran : Bleu nuit sombre (ciel encore nuit)
  },

  // ✨ === MODE LEVER DU SOLEIL === ✨
  // CISCO: Vraies couleurs extraites - Doré éclatant naturel
  sunrise: {
    primary: '#fcd68b',    // Bas de l'écran : Jaune doré éclatant (horizon)
    secondary: '#b5ae8f',  // Milieu de l'écran : Beige doré
    tertiary: '#517a8b'    // Haut de l'écran : Bleu brumeux (zénith)
  },

  // ☀️ === MODE MATIN === ☀️
  // CISCO: Vraies couleurs extraites - Orange matinal naturel
  morning: {
    primary: '#ffd08c',    // Bas de l'écran : Orange pâle matinal (horizon)
    secondary: '#c6c9ce',  // Milieu de l'écran : Gris clair
    tertiary: '#688ab0'    // Haut de l'écran : Bleu poussiéreux (zénith)
  },

  // 🕛 === MODE ZÉNITH (12H) === 🕛
  // CISCO: Vraies couleurs extraites - Bleu pur naturel
  midday: {
    primary: '#e1ecf2',    // Bas de l'écran : Bleu très clair (horizon)
    secondary: '#b6cbed',  // Milieu de l'écran : Bleu clair
    tertiary: '#73a6e0'    // Haut de l'écran : Bleu pur intense (zénith)
  },

  // 🌤️ === MODE APRÈS-MIDI === 🌤️
  // CISCO: Vraies couleurs extraites - Bleu doux naturel
  afternoon: {
    primary: '#eef0f6',    // Bas de l'écran : Blanc bleuté très clair (horizon)
    secondary: '#cfdaec',  // Milieu de l'écran : Bleu très pâle
    tertiary: '#759cd8'    // Haut de l'écran : Bleu doux (zénith)
  },

  // 🌇 === MODE COUCHER DE SOLEIL === 🌇
  // CISCO: Vraies couleurs extraites - Orange et violet naturels
  sunset: {
    primary: '#E38664',    // Bas de l'écran : Orange chaud du soleil (horizon)
    secondary: '#7E627B',  // Milieu de l'écran : Violet rosé
    tertiary: '#5BA0BF'    // Haut de l'écran : Bleu du ciel (zénith)
  },

  // 🌆 === MODE CRÉPUSCULE === 🌆
  // CISCO: Vraies couleurs extraites - Beige et rose naturels
  dusk: {
    primary: '#E8D9BE',    // Bas de l'écran : Beige chaud Luz Alegre (horizon)
    secondary: '#B06A76',  // Milieu de l'écran : Rose poussiéreux Princesa
    tertiary: '#2A3B5A'    // Haut de l'écran : Bleu sombre Azul Colonial (zénith)
  }
};

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

const TRANSITION_MODES = {
  // Transition de la nuit vers l'aube - CORRIGÉE CISCO
  'night-dawn': {
    primary: '#e8b5a8',     // Bas de l'écran : Rose pêche intermédiaire (transition vers aube)
    secondary: '#6a7a9a',   // Milieu de l'écran : Bleu gris plus sombre (transition)
    tertiary: '#2a3b5a',    // Haut de l'écran : Bleu nuit plus sombre (conserve l'effet nuit)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition de l'aube vers le lever du soleil
  'dawn-sunrise': {
    primary: '#fdb5a8',   // Bas de l'écran : Rose pêche vers doré (aube)
    secondary: '#d9b798', // Milieu de l'écran : Transition beige doré
    tertiary: '#b5ae8f',  // Haut de l'écran : Beige doré (lever du soleil)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition du lever du soleil vers le matin
  'sunrise-morning': {
    primary: '#fcd08b',   // Bas de l'écran : Doré vers orange matinal (lever du soleil)
    secondary: '#d0c5a8', // Milieu de l'écran : Transition beige clair
    tertiary: '#a8b5c0',  // Haut de l'écran : Gris clair (matin)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition du matin vers le zénith (midi)
  'morning-midday': {
    primary: '#d8e0f0',   // Bas de l'écran : Orange matinal vers bleu clair (matin)
    secondary: '#c0d2eb', // Milieu de l'écran : Bleu clair
    tertiary: '#a2c8e6',  // Haut de l'écran : Bleu pur (zénith)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition du zénith (midi) vers l'après-midi
  'midday-afternoon': {
    primary: '#e8f0f4',   // Bas de l'écran : Bleu clair maintenu (zénith)
    secondary: '#d2e2f0', // Milieu de l'écran : Transition vers un bleu très pâle
    tertiary: '#b8d0e8',  // Haut de l'écran : Bleu doux (après-midi)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition de l'après-midi vers le coucher du soleil
  'afternoon-sunset': {
    primary: '#e8b598',   // Bas de l'écran : Bleu pâle vers orange chaud (après-midi)
    secondary: '#d89088', // Milieu de l'écran : Transition pêche
    tertiary: '#c8756f',  // Haut de l'écran : Orange chaud (coucher du soleil)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition du coucher du soleil vers le crépuscule
  'sunset-dusk': {
    primary: '#e5a078',   // Bas de l'écran : Orange vers beige (coucher du soleil)
    secondary: '#c88784', // Milieu de l'écran : Transition rose
    tertiary: '#a8759a',  // Haut de l'écran : Rose poussiéreux (crépuscule)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  },
  // Transition du crépuscule vers la nuit
  'dusk-night': {
    primary: '#6a5f8a',   // Bas de l'écran : Beige vers bleu nuit (crépuscule)
    secondary: '#4a456a', // Milieu de l'écran : Transition bleu gris
    tertiary: '#2a3f5a',  // Haut de l'écran : Bleu nuit (nuit)
    percentages: [60, 30, 10] // Pourcentages corrigés : 60% haut, 30% milieu, 10% bas
  }
};

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode?: string; // 🔧 CISCO: Optionnel si contexte disponible
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🔧 CISCO: Utiliser le contexte du cycle de journée si disponible
  const dayCycleContext = useDayCycleOptional();

  // 🔧 CISCO: Déterminer le mode actuel (contexte > props > défaut) avec protection renforcée
  const defaultMode = 'dawn';
  const currentSkyMode = dayCycleContext?.currentPhase || skyMode || defaultMode;

  // 🔧 CISCO: Validation finale du mode pour éviter undefined
  const validatedSkyMode = currentSkyMode && typeof currentSkyMode === 'string' ? currentSkyMode : defaultMode;
  const currentModeRef = useRef(validatedSkyMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -200px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: Fonction pour les transitions d'étoiles - IMPLÉMENTÉE
  const applyStarsTransition = (mode: BackgroundMode, duration: number) => {
    console.log(`⭐ Transition des étoiles vers ${mode} (${duration}s)`);

    // Déclencher la mise à jour du mode dans AstronomicalLayer via l'état
    // Le composant AstronomicalLayer recevra automatiquement le nouveau skyMode
    // et FixedStars se chargera de la transition des étoiles

    // Pas besoin d'action directe ici car le skyMode est passé en props
    // et les useEffect dans FixedStars gèrent les transitions automatiquement
  };



  // 🔧 CISCO: FONCTIONS SPÉCIALISÉES avec vraies couleurs Cisco
  const applyNightMode = () => {
    console.log('🌌 APPLICATION MODE NUIT PROFONDE - Vraies couleurs Cisco');
    const colors = BACKGROUND_MODES.night;
    // 🎨 CISCO: Utilisation des vraies couleurs pour dégradé naturel
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.15;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyDawnMode = () => {
    console.log('🌅 APPLICATION MODE AUBE - Dégradé qui MONTE depuis horizon');
    const colors = BACKGROUND_MODES.dawn;
    // CISCO: Dégradé qui MONTE - Orange/rose en bas (horizon), bleu nuit en haut (ciel)
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 35%, ${colors.tertiary} 100%)`;
    const brightness = 0.4;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyMorningMode = () => {
    console.log('🌤️ APPLICATION MODE MATIN - Vraies couleurs Cisco');
    const colors = BACKGROUND_MODES.morning;
    // 🎨 CISCO: Orange matinal naturel extrait des captures d'écran
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.8;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 20.0, // Mode matin = 20s
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 20.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🔧 CISCO: FONCTION DE RÉINITIALISATION COMPLÈTE pour éviter les conflits
  const resetAllLayers = (targetMode: BackgroundMode) => {
    console.log(`🔄 RÉINITIALISATION COMPLÈTE pour mode: ${targetMode}`);

    // Forcer la mise à jour du mode immédiatement pour que les couches se régénèrent
    currentModeRef.current = targetMode;

    // Appliquer la fonction spécialisée selon le mode
    switch (targetMode) {
      case 'night':
        applyNightMode();
        break;
      case 'dawn':
        applyDawnMode();
        break;
      case 'morning':
        applyMorningMode();
        break;
      default:
        // Pour les autres modes, utiliser la fonction générique
        updateBackgroundSmoothly(targetMode);
        break;
    }

    // Les useEffect des couches (DiurnalLayer, AstronomicalLayer) vont se déclencher
    // automatiquement grâce au changement de skyMode en props
  };

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: RÉINITIALISATION FORCÉE pour modes non adjacents
    const transitionKey = `${currentModeRef.current}-${mode}` as keyof typeof TRANSITION_MODES;
    const isAdjacentTransition = TRANSITION_MODES[transitionKey];

    if (!isAdjacentTransition) {
      console.log(`🔄 TRANSITION NON ADJACENTE détectée: ${currentModeRef.current} → ${mode} - RÉINITIALISATION FORCÉE`);
      resetAllLayers(mode);
    }

    // Transition avec pont si modes adjacents
    if (isAdjacentTransition) {
      console.log(`🌉 Utilisation du pont de transition: ${transitionKey}`);
      // Appliquer d'abord la couleur de transition
      updateBackgroundWithBridge(mode, transitionKey);
    } else {
      // 🔧 AMÉLIORATION: Transition directe mais DOUCE pour modes non adjacents
      console.log(`🎨 Transition directe douce vers: ${mode}`);
      // ✅ CORRECTION: Ne pas changer currentMode immédiatement pour éviter le changement brutal
      updateBackgroundSmoothly(mode); // Cette fonction se chargera de mettre à jour le mode
    }
  };

  // 🔧 NOUVELLE FONCTION: Transition douce même pour les modes non adjacents
  const updateBackgroundSmoothly = (targetMode: BackgroundMode) => {
    if (!gradientRef.current) return;

    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // 🎨 CISCO: Dégradé final avec vraies couleurs extraites des captures d'écran
    const finalGradient = `linear-gradient(to top, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode;
        console.log(`✨ Transition douce vers ${targetMode} terminée ! (Vraies couleurs Cisco)`);
      }
    });

    // 🌊 CISCO: SYNCHRONISATION PARFAITE - 25 secondes avec vraies couleurs
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 25.0, // 🔧 CISCO: Harmonisation temporisateur
      ease: "power1.inOut", // Easing doux pour vraies couleurs
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée avec vraies couleurs
    const transitionDuration = targetMode === 'morning' ? 30.0 : 25.0;
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration,
        ease: "power1.inOut"
      }, 0);
    }

  };

  // 🔧 NOUVELLE FONCTION: Transition avec pont intermédiaire et vraies couleurs
  const updateBackgroundWithBridge = (targetMode: BackgroundMode, transitionKey: keyof typeof TRANSITION_MODES) => {
    if (!gradientRef.current) return;

    const bridgeColors = TRANSITION_MODES[transitionKey];
    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // 🎨 CISCO: Dégradé de pont avec blend naturel des vraies couleurs
    const bridgeGradient = `linear-gradient(to top, ${bridgeColors.primary} 0%, ${bridgeColors.secondary} 60%, ${bridgeColors.tertiary} 100%)`;

    // 🎨 CISCO: Dégradé final avec vraies couleurs Cisco
    const finalGradient = `linear-gradient(to top, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        currentModeRef.current = targetMode;
        setIsTransitioning(false);
        console.log(`✨ Transition avec pont vers ${targetMode} terminée ! (Vraies couleurs Cisco)`);
      }
    });

    // 🌉 CISCO: PHASE 1 - Transition vers le pont (vraies couleurs blend)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: bridgeGradient,
      duration: 12.0,
      ease: "power0.5.inOut",
      force3D: true,
      willChange: "background-image"
    });

    // 🌉 CISCO: PHASE 2 - Transition du pont vers vraies couleurs finales
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 12.0,
      ease: "power0.5.inOut",
      force3D: true,
      willChange: "background-image"
    }, 12.0);

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée avec vraies couleurs
    const transitionDuration = targetMode === 'morning' ? 45.0 : 40.0;
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration,
        ease: "power0.5.inOut"
      }, 0);
    }

    // 🔧 CISCO: SYNCHRONISATION DES ÉTOILES - MÊME TIMING
    timelineRef.current.call(() => {
      applyStarsTransition(targetMode, transitionDuration);
    }, [], 0.1);
  };

  // 🔧 FONCTION SIMPLIFIÉE: Obtenir les couleurs pour un mode donné
  const getColorsForMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: Protection renforcée contre les valeurs undefined/null
    if (!mode || mode === undefined || mode === null) {
      console.warn(`🔧 Mode invalide: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }

    const colors = BACKGROUND_MODES[mode];
    // 🔧 CISCO: Protection contre les modes non définis dans BACKGROUND_MODES
    if (!colors) {
      console.warn(`🔧 Mode non trouvé dans BACKGROUND_MODES: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }
    return colors;
  };

  // 🔧 FONCTION SIMPLIFIÉE: Calculer l'éclairage selon le mode
  const getBrightnessForMode = (mode: BackgroundMode): number => {
    switch (mode) {
      case 'night': return 0.15; // 🔧 CISCO: Réduits de 0.2 à 0.15 pour moins d'éclairage en nuit profonde
      case 'dawn': return 0.4;
      case 'sunrise': return 0.6;
      case 'morning': return 0.8;
      case 'midday': return 1.0;
      case 'afternoon': return 0.8;
      case 'sunset': return 0.6;
      case 'dusk': return 0.4;
      default: return 0.6;
    }
  };


  // 🔧 FONCTION PRINCIPALE: Transition progressive fluide entre modes
  const updateDynamicBackground = (mode?: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter animations actives avant nouvelle transition
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation active dans updateDynamicBackground');
      timelineRef.current.kill();
    }

    if (isTransitioning) { console.log('⏳ Transition déjà en cours, updateDynamicBackground ignoré'); return; }
    if (!gradientRef.current) return;

    const targetMode = mode || skyMode as BackgroundMode;
    const colors = getColorsForMode(targetMode);
    
    // 🎬 INDICATEUR DE TRANSITION
    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé fluide et naturel pour tous les modes
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    
    const brightness = getBrightnessForMode(targetMode);

    console.log(`🎨 Transition progressive fluide vers ${targetMode}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode; // ✅ Mettre à jour le mode courant pour éviter toute re-boucle
        console.log(`✨ Transition vers ${targetMode} terminée !`);
      }
    });

    // 🌅 CISCO: TRANSITION DIRECTE - PLUS DOUCE ET PROGRESSIVE
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: gradient,
      duration: 35.0, // 🔧 CISCO: Plus long pour transition très douce
      ease: "power0.5.inOut", // 🔧 CISCO: Easing très doux
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - TRÈS DOUCE ET PROGRESSIVE
    const transitionDuration = targetMode === 'morning' ? 45.0 : 40.0; // 🔧 CISCO: Beaucoup plus long
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: transitionDuration, // 🔧 CISCO: Transitions très douces
        ease: "power0.5.inOut" // 🔧 CISCO: Easing très doux
      }, 0);
    }

  };

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌌 CISCO: Fonction SIMPLIFIÉE pour nuit profonde - Dégradé seulement
  const triggerNightAnimation = () => {
    console.log('🌌 DÉCLENCHEMENT NUIT PROFONDE - Dégradé seulement');
    updateDynamicBackground('night');
  };

  // Exposer les fonctions globalement
  (window as any).triggerNightAnimation = triggerNightAnimation; // CISCO: Animation nuit profonde

  // Initialisation une seule fois avec vraies couleurs Cisco
  useEffect(() => {
    if (gradientRef.current) {
      const initialColors = getColorsForMode(validatedSkyMode as BackgroundMode);
      // 🔧 CISCO: Dégradé initial avec vraies couleurs extraites
      const initialGradient = `linear-gradient(to top, ${initialColors.primary} 0%, ${initialColors.secondary} 60%, ${initialColors.tertiary} 100%)`;
      gsap.set(gradientRef.current, {
        backgroundImage: initialGradient
      });
      console.log('🎨 Dégradé initial appliqué avec vraies couleurs Cisco');
    }

    createLandscapeZoomAnimation();
    updateDynamicBackground();

    // 🔧 CISCO: Initialiser l'éclairage du paysage pour le mode par défaut (dawn)
    if (landscapeRef.current) {
      const initialBrightness = getBrightnessForMode('dawn');
      gsap.set(landscapeRef.current, {
        filter: `brightness(${initialBrightness})`
      });
      console.log(`💡 Éclairage paysage initialisé pour dawn: brightness(${initialBrightness}) - Vraies couleurs Cisco`);
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🔧 CISCO: Synchronisation avec le contexte du cycle de journée
  useEffect(() => {
    const targetMode = validatedSkyMode;

    if (dayCycleContext) {
      // Mode automatique via contexte du cycle de journée
      console.log(`🌅 Mode automatique (contexte): ${targetMode}`);
      if (targetMode !== (currentModeRef.current as string)) {
        setBackgroundMode(targetMode as BackgroundMode);
      }
    } else if (skyMode) {
      // Mode manuel via props
      console.log(`🎯 Mode manuel (props): ${skyMode}`);
      if (skyMode !== (currentModeRef.current as string)) {
        setBackgroundMode(skyMode as BackgroundMode);
      }
    } else {
      // Mode par défaut
      console.log('🌅 Initialisation mode par défaut: dawn (aube)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [validatedSkyMode, skyMode, dayCycleContext]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="absolute inset-0"
        style={{
          zIndex: 0,
          backgroundAttachment: 'fixed',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode synchronisé avec le contexte */}
      <AstronomicalLayer skyMode={validatedSkyMode as BackgroundMode} />
      <DiurnalLayer skyMode={validatedSkyMode as BackgroundMode} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* Indicateur de transition */}
      {isTransitioning && (
        <div className="fixed top-4 right-4 bg-[#0D9488]/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-50 shadow-lg border border-[#A550F5]/30">
          <div className="flex items-center gap-2">
            <div className="animate-pulse">
              ✨
            </div>
            <span className="text-sm font-medium">
              Transition...
            </span>
          </div>
        </div>
      )}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;

import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
import { useDayCycleOptional } from '../Context/DayCycleContext';
// 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer
// 🔧 CISCO: Suppression des imports GPS/automatisation
// import { useLocation } from '../Context/LocationContext'; // SUPPRIMÉ
// import { useTime } from '../Context/TimeContext'; // SUPPRIMÉ
// import * as SunCalc from 'suncalc'; // SUPPRIMÉ
// 🔧 CISCO: BackgroundController supprimé - Remplacé par DayCycleController

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🔧 SYSTÈME DE PILOTAGE MANUEL SIMPLIFIÉ
// Types pour les modes de fond prédéfinis
type BackgroundMode = 
  | 'dawn'        // Aube
  | 'sunrise'     // Lever du soleil
  | 'morning'     // Matin
  | 'midday'      // Midi
  | 'afternoon'   // Après-midi
  | 'sunset'      // Coucher du soleil
  | 'dusk'        // Crépuscule
  | 'night';      // Nuit

// 🎨 CISCO: PALETTES DE COULEURS NATURELLES ET DOUCES
// ✨ NOUVELLES COULEURS: Pastels naturels inspirés de vrais ciels
// Dégradés fluides sans lignes horizontales visibles

const BACKGROUND_MODES = {
  // 🌌 === MODE NUIT PROFONDE === 🌌
  // Pleine nuit, ciel noir d'encre avec nuances bleu très sombre
  night: {
    primary: '#1a1f3a',   // 🌌 Bleu nuit doux (horizon)
    secondary: '#0f1426', // 🌌 Bleu nuit intermédiaire
    tertiary: '#050811'   // 🌌 Bleu nuit profond (zénith)
  },

  // 🌅 === MODE AUBE === 🌅
  // VRAIES COULEURS CISCO extraites des captures d'écran naturelles
  dawn: {
    primary: '#fec5b9',   // 🌅 Rose pêche doux (horizon) - VRAIE COULEUR
    secondary: '#dc8998', // 🌅 Rose plus intense (milieu) - VRAIE COULEUR
    tertiary: '#787fa1'   // 🌅 Bleu gris lavande (zénith) - VRAIE COULEUR
  },

  // ✨ === MODE LEVER DU SOLEIL === ✨
  // Soleil perce, couleurs chaudes et dorées, mais douces
  sunrise: {
    primary: '#f4c2a1',   // ✨ Pêche douce (horizon)
    secondary: '#f0b08a', // ✨ Abricot tendre (milieu)
    tertiary: '#e8d5b7'   // ✨ Crème dorée (zénith)
  },

  // ☀️ === MODE MATIN === ☀️
  // Soleil levé, ciel bleu clair et frais, tons pastels
  morning: {
    primary: '#c8e6f5',   // ☀️ Bleu ciel très doux (horizon)
    secondary: '#b5ddf0', // ☀️ Bleu clair tendre (milieu)
    tertiary: '#a2d2eb'   // ☀️ Bleu ciel pastel (zénith)
  },

  // 🕛 === MODE ZÉNITH (12H) === 🕛
  // Soleil à l'apogée, bleu pur mais doux
  midday: {
    primary: '#a8d0f0',   // 🕛 Bleu céruléen doux (horizon)
    secondary: '#8fc4eb', // 🕛 Bleu azur tendre (milieu)
    tertiary: '#76b8e6'   // 🕛 Bleu ciel pur (zénith)
  },

  // 🌤️ === MODE APRÈS-MIDI === 🌤️
  // Soleil descend, lumière dorée douce
  afternoon: {
    primary: '#f5e6a3',   // 🌤️ Jaune crème doux (horizon)
    secondary: '#d4e0f0', // 🌤️ Bleu très pâle (milieu)
    tertiary: '#b8d4eb'   // 🌤️ Bleu tendre (zénith)
  },

  // 🌇 === MODE COUCHER DE SOLEIL === 🌇
  // Spectacle du soir, couleurs chaudes mais pastels
  sunset: {
    primary: '#f0a5a8',   // 🌇 Rose saumon doux (horizon)
    secondary: '#f2b5a1', // 🌇 Pêche rosée (milieu)
    tertiary: '#f5d5a8'   // 🌇 Abricot crème (zénith)
  },

  // 🌆 === MODE CRÉPUSCULE === 🌆
  // Après coucher, violets et roses doux avant la nuit
  dusk: {
    primary: '#8a9bc1',   // 🌆 Bleu lavande doux (horizon)
    secondary: '#a68db5', // 🌆 Violet tendre (milieu)
    tertiary: '#c4a5b8'   // 🌆 Rose poudré (zénith)
  }
};

// 🔧 CISCO: Système de couleurs de transition douces et naturelles
const TRANSITION_MODES = {
  'night-dawn': {
    primary: '#3a4a6a',   // Transition nuit vers aube - CISCO
    secondary: '#8a7a9a', // Bleu gris vers rose - CISCO
    tertiary: '#c4a5b8'   // Vers rose pêche - CISCO
  },
  'dawn-sunrise': {
    primary: '#f0b5a8',   // Rose pêche vers doré - CISCO
    secondary: '#e8c5a8', // Transition dorée - CISCO
    tertiary: '#d8b598'   // Vers beige doré - CISCO
  },
  'sunrise-morning': {
    primary: '#e8d5b7',   // Crème dorée vers bleu
    secondary: '#d0e5f0', // Transition bleu très pâle
    tertiary: '#c8e6f5'   // Bleu ciel doux
  },
  'morning-midday': {
    primary: '#b8ddf0',   // Bleu clair vers azur
    secondary: '#a2d2eb', // Bleu ciel pastel
    tertiary: '#8fc4eb'   // Bleu azur tendre
  },
  'midday-afternoon': {
    primary: '#a8d0f0',   // Maintien bleu doux
    secondary: '#c0d8f0', // Transition vers crème
    tertiary: '#e0e8f0'   // Bleu très pâle
  },
  'afternoon-sunset': {
    primary: '#f0e0a8',   // Crème vers pêche
    secondary: '#f2c5a8', // Pêche rosée
    tertiary: '#f0a5a8'   // Rose saumon doux
  },
  'sunset-dusk': {
    primary: '#f2b5a1',   // Pêche vers lavande
    secondary: '#c8a8b5', // Rose poudré
    tertiary: '#a68db5'   // Lavande tendre
  },
  'dusk-night': {
    primary: '#8a9bc1',   // Lavande vers nuit
    secondary: '#5a6b8a', // Bleu gris intermédiaire
    tertiary: '#2a3f5a'   // Bleu nuit doux
  }
};

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode?: string; // 🔧 CISCO: Optionnel si contexte disponible
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🔧 CISCO: Utiliser le contexte du cycle de journée si disponible
  const dayCycleContext = useDayCycleOptional();

  // 🔧 CISCO: Déterminer le mode actuel (contexte > props > défaut) avec protection renforcée
  const defaultMode = 'dawn';
  const currentSkyMode = dayCycleContext?.currentPhase || skyMode || defaultMode;

  // 🔧 CISCO: Validation finale du mode pour éviter undefined
  const validatedSkyMode = currentSkyMode && typeof currentSkyMode === 'string' ? currentSkyMode : defaultMode;
  const currentModeRef = useRef(validatedSkyMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -200px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: Fonction pour les transitions d'étoiles - IMPLÉMENTÉE
  const applyStarsTransition = (mode: BackgroundMode, duration: number) => {
    console.log(`⭐ Transition des étoiles vers ${mode} (${duration}s)`);

    // Déclencher la mise à jour du mode dans AstronomicalLayer via l'état
    // Le composant AstronomicalLayer recevra automatiquement le nouveau skyMode
    // et FixedStars se chargera de la transition des étoiles

    // Pas besoin d'action directe ici car le skyMode est passé en props
    // et les useEffect dans FixedStars gèrent les transitions automatiquement
  };



  // 🔧 CISCO: FONCTIONS SPÉCIALISÉES avec dégradés fluides naturels
  const applyNightMode = () => {
    console.log('🌌 APPLICATION MODE NUIT PROFONDE - Spécialisé');
    const colors = BACKGROUND_MODES.night;
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.15;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyDawnMode = () => {
    console.log('🌅 APPLICATION MODE AUBE - Spécialisé');
    const colors = BACKGROUND_MODES.dawn;
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.tertiary} 100%)`;
    const brightness = 0.4;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyMorningMode = () => {
    console.log('🌤️ APPLICATION MODE MATIN - Spécialisé');
    const colors = BACKGROUND_MODES.morning;
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.8;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 20.0, // Mode matin = 20s
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 20.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🔧 CISCO: FONCTION DE RÉINITIALISATION COMPLÈTE pour éviter les conflits
  const resetAllLayers = (targetMode: BackgroundMode) => {
    console.log(`🔄 RÉINITIALISATION COMPLÈTE pour mode: ${targetMode}`);

    // Forcer la mise à jour du mode immédiatement pour que les couches se régénèrent
    currentModeRef.current = targetMode;

    // Appliquer la fonction spécialisée selon le mode
    switch (targetMode) {
      case 'night':
        applyNightMode();
        break;
      case 'dawn':
        applyDawnMode();
        break;
      case 'morning':
        applyMorningMode();
        break;
      default:
        // Pour les autres modes, utiliser la fonction générique
        updateBackgroundSmoothly(targetMode);
        break;
    }

    // Les useEffect des couches (DiurnalLayer, AstronomicalLayer) vont se déclencher
    // automatiquement grâce au changement de skyMode en props
  };

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: RÉINITIALISATION FORCÉE pour modes non adjacents
    const transitionKey = `${currentModeRef.current}-${mode}` as keyof typeof TRANSITION_MODES;
    const isAdjacentTransition = TRANSITION_MODES[transitionKey];

    if (!isAdjacentTransition) {
      console.log(`🔄 TRANSITION NON ADJACENTE détectée: ${currentModeRef.current} → ${mode} - RÉINITIALISATION FORCÉE`);
      resetAllLayers(mode);
    }

    // Transition avec pont si modes adjacents
    if (isAdjacentTransition) {
      console.log(`🌉 Utilisation du pont de transition: ${transitionKey}`);
      // Appliquer d'abord la couleur de transition
      updateBackgroundWithBridge(mode, transitionKey);
    } else {
      // 🔧 AMÉLIORATION: Transition directe mais DOUCE pour modes non adjacents
      console.log(`🎨 Transition directe douce vers: ${mode}`);
      // ✅ CORRECTION: Ne pas changer currentMode immédiatement pour éviter le changement brutal
      updateBackgroundSmoothly(mode); // Cette fonction se chargera de mettre à jour le mode
    }
  };

  // 🔧 NOUVELLE FONCTION: Transition douce même pour les modes non adjacents
  const updateBackgroundSmoothly = (targetMode: BackgroundMode) => {
    if (!gradientRef.current) return;

    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // Créer le dégradé final avec transition ultra douce et fluide
    const finalGradient = `linear-gradient(to top, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode;
        console.log(`✨ Transition douce vers ${targetMode} terminée !`);
      }
    });

    // 🌊 CISCO: SYNCHRONISATION PARFAITE - 15 secondes avec easing très doux
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 25.0, // 🔧 CISCO: Harmonisation à 25 secondes pour synchronisation avec soleil
      ease: "power1.inOut", // Easing plus doux que power2
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée (durée adaptée selon le mode)
    const transitionDuration = targetMode === 'morning' ? 30.0 : 25.0; // 🔧 CISCO: Mode matin = 30s, autres = 25s
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration, // 🔧 CISCO: Harmonisation adaptée
        ease: "power1.inOut"
      }, 0);
    }

  };

  // 🔧 NOUVELLE FONCTION: Transition avec pont intermédiaire
  const updateBackgroundWithBridge = (targetMode: BackgroundMode, transitionKey: keyof typeof TRANSITION_MODES) => {
    if (!gradientRef.current) return;

    const bridgeColors = TRANSITION_MODES[transitionKey];
    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // 🎨 CISCO: Dégradé de pont fluide et naturel
    const bridgeGradient = `linear-gradient(to top, ${bridgeColors.primary} 0%, ${bridgeColors.secondary} 60%, ${bridgeColors.tertiary} 100%)`;

    // 🎨 CISCO: Dégradé final fluide pour tous les modes
    const finalGradient = `linear-gradient(to top, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        currentModeRef.current = targetMode;
        setIsTransitioning(false);
        console.log(`✨ Transition avec pont vers ${targetMode} terminée !`);
      }
    });

    // 🌉 CISCO: PHASE 1 - Transition vers le pont (PLUS DOUCE - 12 secondes)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: bridgeGradient,
      duration: 12.0, // 🔧 CISCO: Plus long pour transition douce
      ease: "power0.5.inOut", // 🔧 CISCO: Easing plus doux
      force3D: true,
      willChange: "background-image"
    });

    // 🌉 CISCO: PHASE 2 - Transition du pont vers le mode final (PLUS DOUCE - 12 secondes)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 12.0, // 🔧 CISCO: Plus long pour transition douce
      ease: "power0.5.inOut", // 🔧 CISCO: Easing plus doux
      force3D: true,
      willChange: "background-image"
    }, 12.0); // 🔧 CISCO: Démarre après 12 secondes

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - PLUS DOUCE (durée adaptée selon le mode)
    const transitionDuration = targetMode === 'morning' ? 45.0 : 40.0; // 🔧 CISCO: Plus long pour douceur
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration, // 🔧 CISCO: Transitions plus douces
        ease: "power0.5.inOut" // 🔧 CISCO: Easing plus doux
      }, 0);
    }

    // 🔧 CISCO: NUAGES GÉRÉS PAR DiurnalLayer - Plus besoin d'intervention manuelle

    // 🔧 CISCO: SYNCHRONISATION DES ÉTOILES - MÊME TIMING QUE LES NUAGES
    timelineRef.current.call(() => {
      applyStarsTransition(targetMode, transitionDuration);
    }, [], 0.1); // 🔧 CISCO: Même délai pour synchronisation parfaite
  };

  // 🔧 FONCTION SIMPLIFIÉE: Obtenir les couleurs pour un mode donné
  const getColorsForMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: Protection renforcée contre les valeurs undefined/null
    if (!mode || mode === undefined || mode === null) {
      console.warn(`🔧 Mode invalide: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }

    const colors = BACKGROUND_MODES[mode];
    // 🔧 CISCO: Protection contre les modes non définis dans BACKGROUND_MODES
    if (!colors) {
      console.warn(`🔧 Mode non trouvé dans BACKGROUND_MODES: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }
    return colors;
  };

  // 🔧 FONCTION SIMPLIFIÉE: Calculer l'éclairage selon le mode
  const getBrightnessForMode = (mode: BackgroundMode): number => {
    switch (mode) {
      case 'night': return 0.15; // 🔧 CISCO: Réduit de 0.2 à 0.15 pour moins d'éclairage en nuit profonde
      case 'dawn': return 0.4;
      case 'sunrise': return 0.6;
      case 'morning': return 0.8;
      case 'midday': return 1.0;
      case 'afternoon': return 0.8;
      case 'sunset': return 0.6;
      case 'dusk': return 0.4;
      default: return 0.6;
    }
  };


  // 🔧 FONCTION PRINCIPALE: Transition progressive fluide entre modes
  const updateDynamicBackground = (mode?: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter animations actives avant nouvelle transition
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation active dans updateDynamicBackground');
      timelineRef.current.kill();
    }

    if (isTransitioning) { console.log('⏳ Transition déjà en cours, updateDynamicBackground ignoré'); return; }
    if (!gradientRef.current) return;

    const targetMode = mode || skyMode as BackgroundMode;
    const colors = getColorsForMode(targetMode);
    
    // 🎬 INDICATEUR DE TRANSITION
    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé fluide et naturel pour tous les modes
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    
    const brightness = getBrightnessForMode(targetMode);

    console.log(`🎨 Transition progressive fluide vers ${targetMode}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode; // ✅ Mettre à jour le mode courant pour éviter toute re-boucle
        console.log(`✨ Transition vers ${targetMode} terminée !`);
      }
    });

    // 🌅 CISCO: TRANSITION DIRECTE - PLUS DOUCE ET PROGRESSIVE
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: gradient,
      duration: 35.0, // 🔧 CISCO: Plus long pour transition très douce
      ease: "power0.5.inOut", // 🔧 CISCO: Easing très doux
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - TRÈS DOUCE ET PROGRESSIVE
    const transitionDuration = targetMode === 'morning' ? 45.0 : 40.0; // 🔧 CISCO: Beaucoup plus long
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: transitionDuration, // 🔧 CISCO: Transitions très douces
        ease: "power0.5.inOut" // 🔧 CISCO: Easing très doux
      }, 0);
    }

  };

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌌 CISCO: Fonction SIMPLIFIÉE pour nuit profonde - Dégradé seulement
  const triggerNightAnimation = () => {
    console.log('🌌 DÉCLENCHEMENT NUIT PROFONDE - Dégradé seulement');
    updateDynamicBackground('night');
  };

  // Exposer les fonctions globalement
  (window as any).triggerNightAnimation = triggerNightAnimation; // CISCO: Animation nuit profonde

  // Initialisation une seule fois
  useEffect(() => {
    if (gradientRef.current) {
      const initialColors = getColorsForMode(validatedSkyMode as BackgroundMode);
      // 🔧 CISCO: Dégradé initial fluide et naturel
      const initialGradient = `linear-gradient(to top, ${initialColors.primary} 0%, ${initialColors.secondary} 60%, ${initialColors.tertiary} 100%)`;
      gsap.set(gradientRef.current, {
        backgroundImage: initialGradient
      });
    }

    createLandscapeZoomAnimation();
    updateDynamicBackground();

    // 🔧 CISCO: Initialiser l'éclairage du paysage pour le mode par défaut (dawn)
    if (landscapeRef.current) {
      const initialBrightness = getBrightnessForMode('dawn');
      gsap.set(landscapeRef.current, {
        filter: `brightness(${initialBrightness})`
      });
      console.log(`💡 Éclairage paysage initialisé pour dawn: brightness(${initialBrightness})`);
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🔧 CISCO: Synchronisation avec le contexte du cycle de journée
  useEffect(() => {
    const targetMode = validatedSkyMode;

    if (dayCycleContext) {
      // Mode automatique via contexte du cycle de journée
      console.log(`🌅 Mode automatique (contexte): ${targetMode}`);
      if (targetMode !== (currentModeRef.current as string)) {
        setBackgroundMode(targetMode as BackgroundMode);
      }
    } else if (skyMode) {
      // Mode manuel via props
      console.log(`🎯 Mode manuel (props): ${skyMode}`);
      if (skyMode !== (currentModeRef.current as string)) {
        setBackgroundMode(skyMode as BackgroundMode);
      }
    } else {
      // Mode par défaut
      console.log('🌅 Initialisation mode par défaut: dawn (aube)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [validatedSkyMode, skyMode, dayCycleContext]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="absolute inset-0"
        style={{
          zIndex: 0,
          backgroundAttachment: 'fixed',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode synchronisé avec le contexte */}
      <AstronomicalLayer skyMode={validatedSkyMode as BackgroundMode} />
      <DiurnalLayer skyMode={validatedSkyMode as BackgroundMode} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* Indicateur de transition */}
      {isTransitioning && (
        <div className="fixed top-4 right-4 bg-[#0D9488]/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-50 shadow-lg border border-[#A550F5]/30">
          <div className="flex items-center gap-2">
            <div className="animate-pulse">
              ✨
            </div>
            <span className="text-sm font-medium">
              Transition...
            </span>
          </div>
        </div>
      )}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
